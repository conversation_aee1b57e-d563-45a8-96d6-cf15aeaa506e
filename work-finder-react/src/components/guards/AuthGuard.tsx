import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/stores/auth-store';
import { AUTH_ROUTES } from '@/constants/routes';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo 
}: AuthGuardProps) {
  const { isAuthenticated, isInitializing } = useAuthStore();
  const location = useLocation();

  // Still initializing, show loading or return children
  if (isInitializing) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>;
  }

  // User needs to be authenticated but isn't
  if (requireAuth && !isAuthenticated) {
    const from = location.pathname + location.search;
    return <Navigate 
      to={redirectTo || AUTH_ROUTES.LOGIN} 
      state={{ from }} 
      replace 
    />;
  }

  // User is authenticated but trying to access auth pages
  if (!requireAuth && isAuthenticated) {
    const from = (location.state as any)?.from || '/';
    return <Navigate to={from} replace />;
  }

  return <>{children}</>;
}