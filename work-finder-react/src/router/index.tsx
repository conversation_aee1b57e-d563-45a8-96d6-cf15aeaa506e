import { createBrowserRouter, Navigate } from 'react-router-dom';
import { AppLayout } from '@/components/layout';
import { AuthGuard } from '@/components/guards';
import { 
  HomePage, 
  LoginPage, 
  RegisterPage, 
  JobsPage, 
  ApplicationsPage 
} from '@/pages';
import { JobDetailPage } from '@/pages/JobDetailPage';
import { CompaniesPage } from '@/pages/CompaniesPage';
import { CompanyDetailPage } from '@/pages/CompanyDetailPage';
import { SavedJobsPage } from '@/pages/SavedJobsPage';
import { PUBLIC_ROUTES, AUTH_ROUTES, DASHBOARD_ROUTES } from '@/constants/routes';

// Error Boundary Component
function ErrorBoundaryPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Oops! Something went wrong</h1>
        <p className="text-lg text-gray-600 mb-8">
          We're sorry, but something unexpected happened.
        </p>
        <button 
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
        >
          Reload Page
        </button>
      </div>
    </div>
  );
}

// 404 Not Found Page
function NotFoundPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <p className="text-xl text-gray-600 mb-8">
          The page you're looking for doesn't exist.
        </p>
        <a 
          href="/"
          className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors inline-block"
        >
          Go Home
        </a>
      </div>
    </div>
  );
}

// Placeholder pages for routes that aren't implemented yet
function PlaceholderPage({ title }: { title: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{title}</h1>
        <p className="text-lg text-gray-600">
          This page is under construction.
        </p>
      </div>
    </div>
  );
}

export const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout />,
    errorElement: <ErrorBoundaryPage />,
    children: [
      // Public Routes
      {
        path: PUBLIC_ROUTES.HOME,
        element: <HomePage />,
      },
      {
        path: PUBLIC_ROUTES.JOBS,
        element: <JobsPage />,
      },
      {
        path: PUBLIC_ROUTES.JOB_DETAILS,
        element: <JobDetailPage />,
      },
      {
        path: PUBLIC_ROUTES.COMPANIES,
        element: <CompaniesPage />,
      },
      {
        path: PUBLIC_ROUTES.COMPANY_DETAILS,
        element: <CompanyDetailPage />,
      },
      {
        path: PUBLIC_ROUTES.ABOUT,
        element: <PlaceholderPage title="About Us" />,
      },
      {
        path: PUBLIC_ROUTES.CONTACT,
        element: <PlaceholderPage title="Contact Us" />,
      },
      {
        path: PUBLIC_ROUTES.PRIVACY,
        element: <PlaceholderPage title="Privacy Policy" />,
      },
      {
        path: PUBLIC_ROUTES.TERMS,
        element: <PlaceholderPage title="Terms of Service" />,
      },

      // Authentication Routes (redirect to home if already authenticated)
      {
        path: AUTH_ROUTES.LOGIN,
        element: <LoginPage />,
      },
      {
        path: AUTH_ROUTES.REGISTER,
        element: <RegisterPage />,
      },
      {
        path: AUTH_ROUTES.FORGOT_PASSWORD,
        element: (
          <AuthGuard requireAuth={false}>
            <PlaceholderPage title="Forgot Password" />
          </AuthGuard>
        ),
      },
      {
        path: AUTH_ROUTES.RESET_PASSWORD,
        element: (
          <AuthGuard requireAuth={false}>
            <PlaceholderPage title="Reset Password" />
          </AuthGuard>
        ),
      },

      // Dashboard Routes (require authentication)
      {
        path: DASHBOARD_ROUTES.APPLICATIONS,
        element: <ApplicationsPage />,
      },
      {
        path: DASHBOARD_ROUTES.SAVED_JOBS,
        element: <SavedJobsPage />,
      },
      {
        path: DASHBOARD_ROUTES.PROFILE,
        element: (
          <AuthGuard>
            <PlaceholderPage title="Complete Profile" />
          </AuthGuard>
        ),
      },

      // Legacy dashboard route redirect
      {
        path: '/dashboard',
        element: <Navigate to={DASHBOARD_ROUTES.APPLICATIONS} replace />,
      },

      // Catch all route - 404
      {
        path: '*',
        element: <NotFoundPage />,
      },
    ],
  },
]);